package web

import (
	"fmt"
	"net/http"
)

func handleOptions(w http.ResponseWriter, _ *http.Request) {
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
	w.<PERSON>er().Set("Access-Control-Allow-Methods", "GET, POST, PATCH, DELETE, PUT")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Write<PERSON>eader(http.StatusNoContent)
}

type DesignsReadHandler interface {
	HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request)
	HandleListDesignsForProject(w http.ResponseWriter, r *http.Request)
	HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request)
}

type DesignsWriteHandler interface {
	HandlePost(w http.ResponseWriter, r *http.Request)
	HandlePut(w http.ResponseWriter, r *http.Request)
	HandlePatch(w http.ResponseWriter, r *http.Request)
	HandleDelete(w http.ResponseWriter, r *http.Request)
	HandlePutAllDesignsForProject(w http.ResponseWriter, r *http.Request)
}

type PresetsReadHandler interface {
	HandleGetPreset(w http.ResponseWriter, r *http.Request)
}

type TemplatesReadHandler interface {
	HandleGetTemplate(w http.ResponseWriter, r *http.Request)
	HandleGetAllTemplates(w http.ResponseWriter, r *http.Request)
}

type TemplatesWriteHandler interface {
	HandlePutTemplate(w http.ResponseWriter, r *http.Request)
}

func RegisterGlobalHandlers(readHandler DesignsReadHandler) {
	http.HandleFunc("GET /projects/designs", readHandler.HandleGetAllDesignsForMultipleProjects)
	http.HandleFunc("/healthz", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
}

func RegisterPresetHandlers(prefix string, readHandler PresetsReadHandler) {
	http.HandleFunc(fmt.Sprintf("GET %s/{presetId}", prefix), readHandler.HandleGetPreset)
}

func RegisterTemplateHandlers(prefix string, readHandler TemplatesReadHandler, writeHandler TemplatesWriteHandler) {
	http.HandleFunc(fmt.Sprintf("GET %s/{templateId}", prefix), readHandler.HandleGetTemplate)
	http.HandleFunc(fmt.Sprintf("GET %s", prefix), readHandler.HandleGetAllTemplates)
	http.HandleFunc(fmt.Sprintf("GET %s/", prefix), readHandler.HandleGetAllTemplates)
	http.HandleFunc(fmt.Sprintf("PUT %s/{templateId}", prefix), writeHandler.HandlePutTemplate)
}

func RegisterDesignHandlers(prefix string, readHandler DesignsReadHandler, writeHandler DesignsWriteHandler) {
	http.HandleFunc(fmt.Sprintf("OPTIONS %s/designs/", prefix), handleOptions)
	http.HandleFunc(fmt.Sprintf("GET %s/designs/{designId}", prefix), readHandler.HandleGetSpecifiedDesign)
	http.HandleFunc(fmt.Sprintf("GET %s/designs/", prefix), readHandler.HandleListDesignsForProject)
	http.HandleFunc(fmt.Sprintf("GET %s/designs", prefix), readHandler.HandleListDesignsForProject)
	http.HandleFunc(fmt.Sprintf("POST %s/designs/", prefix), writeHandler.HandlePost)
	http.HandleFunc(fmt.Sprintf("POST %s/designs", prefix), writeHandler.HandlePost)
	http.HandleFunc(fmt.Sprintf("PUT %s/designs/{designId}", prefix), writeHandler.HandlePut)
	http.HandleFunc(fmt.Sprintf("PUT %s/designs/", prefix), writeHandler.HandlePutAllDesignsForProject)
	http.HandleFunc(fmt.Sprintf("PUT %s/designs", prefix), writeHandler.HandlePutAllDesignsForProject)
	http.HandleFunc(fmt.Sprintf("PATCH %s/designs/{designId}", prefix), writeHandler.HandlePatch)
	http.HandleFunc(fmt.Sprintf("DELETE %s/designs/{designId}", prefix), writeHandler.HandleDelete)
}
