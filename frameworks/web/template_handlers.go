package web

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
)

type TemplateQueryHandler struct {
	logger             *slog.Logger
	templateController *controllers.TemplateRetrievalController
}

func NewTemplateQueryHandler(logger *slog.Logger, templateController *controllers.TemplateRetrievalController) *TemplateQueryHandler {
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplateQueryHandler{
		logger:             logger,
		templateController: templateController,
	}
}

func (h *TemplateQueryHandler) HandleGetTemplate(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	templateId := r.<PERSON>ue("templateId")
	presenter := presenters.NewTemplatesPresenter(h.logger, w)

	if len(templateId) == 2 {
		h.templateController.FetchTemplateByLegacyId(ctx, templateId, presenter)
		return
	}

	templateUUID, err := uuid.Parse(templateId)
	if err != nil {
		http.Error(w, "Invalid template ID: must be either a 2-character legacy ID or a valid UUID", http.StatusBadRequest)
		return
	}
	h.templateController.FetchTemplate(ctx, templateUUID, presenter)
}

func (h *TemplateQueryHandler) HandleGetAllTemplates(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	presenter := presenters.NewTemplatesPresenter(h.logger, w)
	h.templateController.FetchAllTemplates(ctx, presenter)
}

// TemplateWriteHandler handles template write operations
type TemplateWriteHandler struct {
	logger     *slog.Logger
	controller *controllers.TemplateWriteController
}

func NewTemplateWriteHandler(logger *slog.Logger, controller *controllers.TemplateWriteController) *TemplateWriteHandler {
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplateWriteHandler{
		logger:     logger,
		controller: controller,
	}
}

func (h *TemplateWriteHandler) parseTemplate(r *http.Request) (adapters.Template, error) {
	var template adapters.Template
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return template, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return template, fmt.Errorf("empty request body")
	}
	var payload map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return template, fmt.Errorf("invalid JSON payload: %w", err)
	}
	// TODO: Add schema validation
	if err := json.Unmarshal(body, &template); err != nil {
		return template, err
	}
	return template, nil
}

func (h *TemplateWriteHandler) HandlePutTemplate(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PUT template request...")

	templateId := r.PathValue("templateId")
	templateUUID, err := uuid.Parse(templateId)
	if err != nil {
		http.Error(w, "Invalid template UUID", http.StatusBadRequest)
		return
	}

	presenter := presenters.NewTemplateCreationOutcomePresenter(h.logger, w)
	template, err := h.parseTemplate(r)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing template payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if err := template.AlignId(templateId); err != nil {
		http.Error(w, "Template ID in payload does not match URL", http.StatusBadRequest)
		return
	}

	h.controller.SaveTemplate(ctx, templateUUID, template, presenter)
}
