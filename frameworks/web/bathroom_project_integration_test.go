package web_test

import (
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
)

func TestBathroomProjectEndpointIntegration(t *testing.T) {
	// Create a new HTTP mux to test the routing
	mux := http.NewServeMux()
	
	// Create and register the bathroom project handler
	logger := slog.Default()
	handler := web.NewBathroomProjectHandler(logger)
	
	// Register the handler with the mux (simulating what happens in main.go)
	mux.HandleFunc("GET /bathroom-project/random", handler.HandleGetRandomBathroomProject)
	
	// Create a test server
	server := httptest.NewServer(mux)
	defer server.Close()
	
	// Make a request to the endpoint
	resp, err := http.Get(server.URL + "/bathroom-project/random")
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()
	
	// Check the response status
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
	
	// Check content type
	expectedContentType := "application/json"
	if contentType := resp.Header.Get("Content-Type"); contentType != expectedContentType {
		t.Errorf("Expected content type %s, got %s", expectedContentType, contentType)
	}
	
	// Parse the response
	var project map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		t.Fatalf("Failed to decode JSON response: %v", err)
	}
	
	// Validate the response structure matches the JSON schema
	requiredFields := []string{"name", "cost_usd", "num_items", "area_sq_ft", "updated_at"}
	for _, field := range requiredFields {
		if _, exists := project[field]; !exists {
			t.Errorf("Required field %s is missing from response", field)
		}
	}
	
	t.Logf("Generated project: %+v", project)
}

func TestBathroomProjectEndpointWrongMethod(t *testing.T) {
	// Create a new HTTP mux to test the routing
	mux := http.NewServeMux()
	
	// Create and register the bathroom project handler
	logger := slog.Default()
	handler := web.NewBathroomProjectHandler(logger)
	
	// Register the handler with the mux
	mux.HandleFunc("GET /bathroom-project/random", handler.HandleGetRandomBathroomProject)
	
	// Create a test server
	server := httptest.NewServer(mux)
	defer server.Close()
	
	// Make a POST request (should fail since we only accept GET)
	resp, err := http.Post(server.URL+"/bathroom-project/random", "application/json", nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()
	
	// Should return 405 Method Not Allowed
	if resp.StatusCode != http.StatusMethodNotAllowed {
		t.Errorf("Expected status code %d for POST request, got %d", http.StatusMethodNotAllowed, resp.StatusCode)
	}
}
