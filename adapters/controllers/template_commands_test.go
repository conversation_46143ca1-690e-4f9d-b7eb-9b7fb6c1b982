package controllers_test

import (
	"context"
	"log/slog"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type mockTemplateRepository struct {
	mock.Mock
}

func (m *mockTemplateRepository) InsertTemplate(ctx context.Context, template usecases.Template, legacyId string) (uuid.UUID, error) {
	args := m.Called(ctx, template, legacyId)
	return args.Get(0).(uuid.UUID), args.Error(1)
}

func (m *mockTemplateRepository) ReadTemplate(ctx context.Context, templateId uuid.UUID) (usecases.Template, error) {
	args := m.Called(ctx, templateId)
	return args.Get(0).(usecases.Template), args.Error(1)
}

func (m *mockTemplateRepository) ReadTemplateByLegacyId(ctx context.Context, legacyId string) (usecases.Template, error) {
	args := m.Called(ctx, legacyId)
	return args.Get(0).(usecases.Template), args.Error(1)
}

func (m *mockTemplateRepository) ReadAllTemplates(ctx context.Context) ([]usecases.Template, error) {
	args := m.Called(ctx)
	return args.Get(0).([]usecases.Template), args.Error(1)
}

type mockTemplateCreationOutcomePresenter struct {
	mock.Mock
}

func (m *mockTemplateCreationOutcomePresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockTemplateCreationOutcomePresenter) ConveySuccessWithNewResource(template usecases.Template) {
	m.Called(template)
}

func TestNewTemplateWriteController(t *testing.T) {
	logger := slog.Default()

	t.Run("should create controller with valid creator", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		creator := usecases.NewTemplateCreater(repo, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)
		assert.NotNil(t, controller)
	})

	t.Run("should panic with nil creator", func(t *testing.T) {
		assert.Panics(t, func() {
			controllers.NewTemplateWriteController(nil, logger)
		})
	})
}

func TestTemplateWriteController_SaveTemplate(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	templateId := uuid.New()

	testTemplate := adapters.Template{
		ID:              templateId.String(),
		Name:            "Test Template",
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Description:     "A test template",
		Inspiration:     "Test inspiration",
		Atmosphere:      "cozy, modern",
		ColorPalette:    "#FFFFFF, #000000",
		MaterialPalette: "wood, metal",
		Materials: adapters.Materials{
			VanityDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
			FaucetDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
		},
	}

	t.Run("should save template successfully", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creator := usecases.NewTemplateCreater(repo, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)

		// Set up expectations
		repo.On("InsertTemplate", ctx, mock.MatchedBy(func(t usecases.Template) bool {
			return t.ID == templateId && t.Name == "Test Template"
		}), templateId.String()).Return(templateId, nil)
		presenter.On("ConveySuccessWithNewResource", mock.Anything).Return()

		// Act
		controller.SaveTemplate(ctx, templateId, testTemplate, presenter)

		// Assert
		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should align template ID with URL parameter", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creator := usecases.NewTemplateCreater(repo, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)

		// Template with different ID
		differentId := uuid.New()
		templateWithDifferentId := testTemplate
		templateWithDifferentId.ID = differentId.String()

		// Set up expectations - should use the URL templateId, not the template's ID
		// The legacyId parameter should be the original template ID (differentId)
		repo.On("InsertTemplate", ctx, mock.MatchedBy(func(t usecases.Template) bool {
			return t.ID == templateId // Should be aligned to URL parameter
		}), differentId.String()).Return(templateId, nil)
		presenter.On("ConveySuccessWithNewResource", mock.Anything).Return()

		// Act
		controller.SaveTemplate(ctx, templateId, templateWithDifferentId, presenter)

		// Assert
		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle conversion error", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creator := usecases.NewTemplateCreater(repo, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)

		// Create invalid template (mismatched vanity/faucet dict lengths)
		invalidTemplate := testTemplate
		invalidTemplate.Materials.VanityDict = map[string]uuid.UUID{"24": uuid.New()}
		invalidTemplate.Materials.FaucetDict = map[string]uuid.UUID{"24": uuid.New(), "36": uuid.New()}

		// Set up expectations
		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		// Act
		controller.SaveTemplate(ctx, templateId, invalidTemplate, presenter)

		// Assert
		presenter.AssertExpectations(t)
		repo.AssertNotCalled(t, "InsertTemplate")
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		creator := usecases.NewTemplateCreater(repo, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)

		assert.Panics(t, func() {
			controller.SaveTemplate(ctx, templateId, testTemplate, nil)
		})
	})
}
