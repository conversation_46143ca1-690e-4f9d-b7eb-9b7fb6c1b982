package presenters_test

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewTemplatesPresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewTemplatesPresenter(logger, w)
	assert.NotNil(t, presenter)
}

func TestTemplatesPresenter_PresentTemplate(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(logger, w)

	testTemplate := usecases.Template{
		ID:              uuid.New(),
		Name:            "Test Template",
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Description:     "A test template",
		Inspiration:     "Test inspiration",
		Atmosphere:      []string{"cozy", "modern"},
		ColorPalette:    []string{"blue", "gold"},
		MaterialPalette: []string{"wood", "metal"},
	}

	presenter.PresentTemplate(ctx, testTemplate)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))

	var responseWrapper map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the template from the data field
	templateData, ok := responseWrapper["data"].(map[string]any)
	require.True(t, ok, "Expected 'data' field to contain template object")

	assert.Equal(t, testTemplate.ID.String(), templateData["id"])
	assert.Equal(t, testTemplate.Name, templateData["name"])
}

func TestTemplatesPresenter_PresentTemplates(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(logger, w)

	testTemplates := []usecases.Template{
		{
			ID:          uuid.New(),
			Name:        "Template 1",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		{
			ID:          uuid.New(),
			Name:        "Template 2",
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
	}

	presenter.PresentTemplates(ctx, testTemplates)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	var responseWrapper map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the templates array from the data field
	templatesData, ok := responseWrapper["data"].([]any)
	require.True(t, ok, "Expected 'data' field to contain templates array")

	assert.Len(t, templatesData, 2)

	// Check first template
	template1, ok := templatesData[0].(map[string]any)
	require.True(t, ok)
	assert.Equal(t, testTemplates[0].Name, template1["name"])

	// Check second template
	template2, ok := templatesData[1].(map[string]any)
	require.True(t, ok)
	assert.Equal(t, testTemplates[1].Name, template2["name"])
}

func TestNewTemplateCreationOutcomePresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)
	assert.NotNil(t, presenter)
}

func TestTemplateCreationOutcomePresenter_ConveySuccessWithNewResource(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

	testTemplate := usecases.Template{
		ID:              uuid.New(),
		Name:            "Test Template",
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Description:     "A test template",
		Inspiration:     "Test inspiration",
		Atmosphere:      []string{"cozy", "modern"},
		ColorPalette:    []string{"#FFFFFF", "#000000"},
		MaterialPalette: []string{"wood", "metal"},
	}

	presenter.ConveySuccessWithNewResource(testTemplate)

	assert.Equal(t, http.StatusCreated, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Contains(t, w.Header().Get("Location"), testTemplate.ID.String())

	// Verify response body contains the template wrapped in data field
	var responseWrapper map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the template from the data field
	templateData, ok := responseWrapper["data"].(map[string]any)
	require.True(t, ok, "Expected 'data' field to contain template object")

	assert.Equal(t, testTemplate.ID.String(), templateData["id"])
	assert.Equal(t, testTemplate.Name, templateData["name"])
}

func TestTemplateCreationOutcomePresenter_PresentError(t *testing.T) {
	logger := slog.Default()

	testCases := []struct {
		name           string
		error          error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "invalid payload error",
			error:          usecases.ErrInvalidPayload,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid payload",
		},
		{
			name:           "not found error",
			error:          usecases.ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "not found",
		},
		{
			name:           "conflict error",
			error:          usecases.ErrConflict,
			expectedStatus: http.StatusConflict,
			expectedBody:   "Template with the same ID already exists",
		},
		{
			name:           "generic error",
			error:          assert.AnError,
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   assert.AnError.Error(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

			presenter.PresentError(tc.error)

			assert.Equal(t, tc.expectedStatus, w.Code)
			assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
			assert.Contains(t, w.Body.String(), tc.expectedBody)
		})
	}
}

func TestTemplateCreationOutcomePresenter_ConveySuccessWithNewResource_InvalidTemplate(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

	// Template with nil/zero UUID
	invalidTemplate := usecases.Template{
		ID:   uuid.Nil,
		Name: "Invalid Template",
	}

	presenter.ConveySuccessWithNewResource(invalidTemplate)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "Missing/invalid ID in created template")
}
